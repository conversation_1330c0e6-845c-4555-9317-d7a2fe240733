import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { LoadingSpinner, ErrorMessage } from '../components/ui/StatusMessages';
import { CsvExportButton } from '../components/admin/CsvExportButton';

interface Energieausweis {
  id: string;
  created_at: string | null; // Allow null for created_at
  certificate_type: string | null; // Allow null for certificate_type
  payment_status: string | null; // Allow null for payment_status
  order_number: string | null;
  // Add other fields as needed for display or export
}

interface PaymentAnalytics {
  total_certificates: number;
  paid_certificates: number;
  failed_certificates: number;
  expired_certificates: number;
  disputed_certificates: number;
  unpaid_certificates: number;
  total_revenue_cents: number;
  conversion_rate: number;
}

interface WebhookEventSummary {
  event_type: string;
  processing_status: string;
  count: number;
  latest_event: string;
}

interface PaymentAttemptSummary {
  attempt_status: string;
  count: number;
  avg_session_duration: number;
  latest_attempt: string;
}

export const AdminPage = () => {
  const queryClient = useQueryClient();
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  const { data: energieausweise, isLoading, error } = useQuery<Energieausweis[] | undefined>({
    queryKey: ['adminEnergieausweise'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('id, created_at, certificate_type, payment_status, order_number'); // Select order_number

      if (error) throw error;
      return data || [];
    },
  });

  // Payment analytics query
  const { data: paymentAnalytics, isLoading: analyticsLoading } = useQuery<PaymentAnalytics>({
    queryKey: ['paymentAnalytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_payment_analytics');
      if (error) throw error;
      return data as unknown as PaymentAnalytics;
    },
  });

  // Webhook events summary
  const { data: webhookEvents, isLoading: webhookLoading } = useQuery<WebhookEventSummary[]>({
    queryKey: ['webhookEventsSummary'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stripe_webhook_events')
        .select('event_type, processing_status, created_at')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group by event_type and processing_status
      const grouped = data.reduce((acc: Record<string, WebhookEventSummary>, event) => {
        const key = `${event.event_type}-${event.processing_status}`;
        if (!acc[key]) {
          acc[key] = {
            event_type: event.event_type,
            processing_status: event.processing_status,
            count: 0,
            latest_event: event.created_at || new Date().toISOString()
          };
        }
        acc[key].count++;
        if (event.created_at && event.created_at > acc[key].latest_event) {
          acc[key].latest_event = event.created_at;
        }
        return acc;
      }, {});

      return Object.values(grouped);
    },
  });

  // Payment attempts summary
  const { data: paymentAttempts, isLoading: attemptsLoading } = useQuery<PaymentAttemptSummary[]>({
    queryKey: ['paymentAttemptsSummary'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('payment_attempts')
        .select('attempt_status, session_duration_seconds, created_at')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group by attempt_status
      const grouped = data.reduce((acc: Record<string, PaymentAttemptSummary>, attempt) => {
        const key = attempt.attempt_status;
        if (!acc[key]) {
          acc[key] = {
            attempt_status: attempt.attempt_status,
            count: 0,
            avg_session_duration: 0,
            latest_attempt: attempt.created_at || new Date().toISOString()
          };
        }
        acc[key].count++;
        if (attempt.session_duration_seconds) {
          acc[key].avg_session_duration =
            (acc[key].avg_session_duration * (acc[key].count - 1) + attempt.session_duration_seconds) / acc[key].count;
        }
        if (attempt.created_at && attempt.created_at > acc[key].latest_attempt) {
          acc[key].latest_attempt = attempt.created_at;
        }
        return acc;
      }, {});

      return Object.values(grouped);
    },
  });

  // Auto-refresh functionality
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (autoRefresh) {
      interval = setInterval(() => {
        queryClient.invalidateQueries({ queryKey: ['adminEnergieausweise'] });
        queryClient.invalidateQueries({ queryKey: ['paymentAnalytics'] });
        queryClient.invalidateQueries({ queryKey: ['webhookEventsSummary'] });
        queryClient.invalidateQueries({ queryKey: ['paymentAttemptsSummary'] });
        setLastRefresh(new Date());
      }, 30000); // Refresh every 30 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, queryClient]);

  const handleManualRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['adminEnergieausweise'] });
    queryClient.invalidateQueries({ queryKey: ['paymentAnalytics'] });
    queryClient.invalidateQueries({ queryKey: ['webhookEventsSummary'] });
    queryClient.invalidateQueries({ queryKey: ['paymentAttemptsSummary'] });
    setLastRefresh(new Date());
  };

  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR'
    }).format(cents / 100);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <LoadingSpinner message="Lade Energieausweise..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <ErrorMessage message={`Fehler beim Laden der Energieausweise: ${error.message}`} />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Admin-Dashboard</h1>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Letzte Aktualisierung: {lastRefresh.toLocaleTimeString('de-DE')}
          </div>
          <button
            onClick={handleManualRefresh}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Aktualisieren
          </button>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
            <span className="ml-2 text-sm text-gray-700">Auto-Aktualisierung (30s)</span>
          </label>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Energieausweise</h2>
      </div>
      {/* Certificates Content */}
      {energieausweise && energieausweise.length > 0 ? (
        <div className="overflow-x-auto shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Bestellnummer
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Erstellt Am
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Zertifikatstyp
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Zahlungsstatus
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Aktionen
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {energieausweise.map((ea) => (
                <tr key={ea.id}>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.order_number || 'N/A'}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.created_at ? new Date(ea.created_at).toLocaleDateString('de-DE') : 'N/A'}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.certificate_type}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.payment_status}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <CsvExportButton
                      certificateId={ea.id}
                      certificateType={ea.certificate_type}
                      orderNumber={ea.order_number}
                      className="text-xs"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="border border-gray-200 rounded-md p-4 text-center text-gray-500">
          Keine Energieausweise gefunden.
        </div>
      )}

      {/* Payment Analytics Section */}
      <div className="my-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Zahlungsanalyse</h2>
        {analyticsLoading ? (
          <LoadingSpinner message="Lade Zahlungsanalyse..." />
        ) : paymentAnalytics ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Gesamt Zertifikate</h3>
              <p className="text-2xl font-bold text-gray-900">{paymentAnalytics.total_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Bezahlt</h3>
              <p className="text-2xl font-bold text-green-600">{paymentAnalytics.paid_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Fehlgeschlagen</h3>
              <p className="text-2xl font-bold text-red-600">{paymentAnalytics.failed_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Abgelaufen</h3>
              <p className="text-2xl font-bold text-yellow-600">{paymentAnalytics.expired_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Streitig</h3>
              <p className="text-2xl font-bold text-purple-600">{paymentAnalytics.disputed_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Unbezahlt</h3>
              <p className="text-2xl font-bold text-gray-600">{paymentAnalytics.unpaid_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Gesamtumsatz</h3>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(paymentAnalytics.total_revenue_cents)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Konversionsrate</h3>
              <p className="text-2xl font-bold text-blue-600">{paymentAnalytics.conversion_rate}%</p>
            </div>
          </div>
        ) : (
          <ErrorMessage message="Fehler beim Laden der Zahlungsanalyse" />
        )}
      </div>

      {/* Webhook Events Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Webhook-Events (letzte 7 Tage)</h2>
        {webhookLoading ? (
          <LoadingSpinner message="Lade Webhook-Events..." />
        ) : webhookEvents && webhookEvents.length > 0 ? (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event-Typ</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Letztes Event</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {webhookEvents.map((event, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{event.event_type}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        event.processing_status === 'handled' ? 'bg-green-100 text-green-800' :
                        event.processing_status === 'error' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {event.processing_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{event.count}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(event.latest_event).toLocaleString('de-DE')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">Keine Webhook-Events in den letzten 7 Tagen gefunden.</p>
        )}
      </div>

      {/* Payment Attempts Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Zahlungsversuche (letzte 7 Tage)</h2>
        {attemptsLoading ? (
          <LoadingSpinner message="Lade Zahlungsversuche..." />
        ) : paymentAttempts && paymentAttempts.length > 0 ? (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ø Sitzungsdauer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Letzter Versuch</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentAttempts.map((attempt, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        attempt.attempt_status === 'succeeded' ? 'bg-green-100 text-green-800' :
                        attempt.attempt_status === 'failed' ? 'bg-red-100 text-red-800' :
                        attempt.attempt_status === 'abandoned' ? 'bg-yellow-100 text-yellow-800' :
                        attempt.attempt_status === 'expired' ? 'bg-gray-100 text-gray-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {attempt.attempt_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{attempt.count}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {attempt.avg_session_duration > 0 ? formatDuration(Math.round(attempt.avg_session_duration)) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(attempt.latest_attempt).toLocaleString('de-DE')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">Keine Zahlungsversuche in den letzten 7 Tagen gefunden.</p>
        )}
      </div>


    </div>
  );
};
